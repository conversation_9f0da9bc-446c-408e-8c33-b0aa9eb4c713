#ifndef ZEXUAN_NET_TCP_CONNECTOR_HPP
#define ZEXUAN_NET_TCP_CONNECTOR_HPP

#include <functional>
#include <memory>

#include "zexuan/net/address.hpp"
#include "zexuan/net/channel.hpp"
#include "zexuan/net/callbacks.hpp"

namespace zexuan {
  namespace net {

    class EventLoop;

    class TcpConnector : public std::enable_shared_from_this<TcpConnector> {
    public:
      TcpConnector(EventLoop* loop, const zexuan::net::Address& serverAddr);
      ~TcpConnector();

      // 禁用拷贝构造和赋值
      TcpConnector(const TcpConnector&) = delete;
      TcpConnector& operator=(const TcpConnector&) = delete;

      void setNewConnectionCallback(const NewConnectionCallback& cb) {
        newConnectionCallback_ = cb;
      }

      void start();    // can be called in any thread
      void restart();  // must be called in loop thread
      void stop();     // can be called in any thread

      const zexuan::net::Address& serverAddress() const { return serverAddr_; }

    private:
      enum States { kDisconnected, kConnecting, kConnected };
      static const int kMaxRetryDelayMs = 30 * 1000;
      static const int kInitRetryDelayMs = 500;

      void setState(States s) { state_ = s; }
      void startInLoop();
      void stopInLoop();
      void connect();
      void connecting(int sockfd);
      void handleWrite();
      void handleError();
      void retry(int sockfd);
      int removeAndResetChannel();
      void resetChannel();

      EventLoop* loop_;
      zexuan::net::Address serverAddr_;
      bool connect_;  // atomic
      States state_;  // FIXME: use atomic variable
      std::unique_ptr<Channel> channel_;
      NewConnectionCallback newConnectionCallback_;
      int retryDelayMs_;
      int64_t timerId_;
    };

  }  // namespace net
}  // namespace zexuan

#endif  // ZEXUAN_NET_TCP_CONNECTOR_HPP
