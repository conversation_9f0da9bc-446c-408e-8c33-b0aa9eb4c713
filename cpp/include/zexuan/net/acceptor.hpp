#ifndef ZEXUAN_NET_TCP_ACCEPTOR_HPP
#define ZEXUAN_NET_TCP_ACCEPTOR_HPP

#include <functional>

#include "zexuan/net/channel.hpp"
#include "zexuan/net/socket.hpp"

namespace zexuan {
  namespace net {

    class EventLoop;

    ///
    /// Acceptor of incoming TCP connections.
    ///
    class TcpAcceptor {
    public:
      typedef std::function<void(int sockfd, const zexuan::net::Address&)> NewConnectionCallback;

      TcpAcceptor(EventLoop* loop, const zexuan::net::Address& listenAddr, bool reuseport);
      ~TcpAcceptor();

      // 禁用拷贝构造和赋值
      TcpAcceptor(const TcpAcceptor&) = delete;
      TcpAcceptor& operator=(const TcpAcceptor&) = delete;

      void setNewConnectionCallback(const NewConnectionCallback& cb) {
        newConnectionCallback_ = cb;
      }

      void listen();

      bool listening() const { return listening_; }

      // Deprecated, use the correct spelling one above.
      // Leave the wrong spelling here in case one needs to grep it for error messages.
      // bool listenning() const { return listening(); }

    private:
      void handleRead(std::chrono::system_clock::time_point);

      EventLoop* loop_;
      zexuan::net::Socket acceptSocket_;
      Channel acceptChannel_;
      NewConnectionCallback newConnectionCallback_;
      bool listening_;
      int idleFd_;
    };

  }  // namespace net
}  // namespace zexuan

#endif  // ZEXUAN_NET_TCP_ACCEPTOR_HPP
